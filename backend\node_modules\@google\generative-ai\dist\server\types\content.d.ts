/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
export * from "./function-calling";
/**
 * Content type for both prompts and response candidates.
 * @public
 */
export interface Content {
    role: string;
    parts: Part[];
}
/**
 * Content part - includes text or image part types.
 * @public
 */
export type Part = TextPart | InlineDataPart | FunctionCallPart | FunctionResponsePart | FileDataPart | ExecutableCodePart | CodeExecutionResultPart;
/**
 * Content part interface if the part represents a text string.
 * @public
 */
export interface TextPart {
    text: string;
    inlineData?: never;
    functionCall?: never;
    functionResponse?: never;
    fileData?: never;
    executableCode?: never;
    codeExecutionResult?: never;
}
/**
 * Content part interface if the part represents an image.
 * @public
 */
export interface InlineDataPart {
    text?: never;
    inlineData: GenerativeContentBlob;
    functionCall?: never;
    functionResponse?: never;
    fileData?: never;
    executableCode?: never;
    codeExecutionResult?: never;
}
/**
 * Content part interface if the part represents a FunctionCall.
 * @public
 */
export interface FunctionCallPart {
    text?: never;
    inlineData?: never;
    functionCall: FunctionCall;
    functionResponse?: never;
    fileData?: never;
    executableCode?: never;
    codeExecutionResult?: never;
}
/**
 * Content part interface if the part represents FunctionResponse.
 * @public
 */
export interface FunctionResponsePart {
    text?: never;
    inlineData?: never;
    functionCall?: never;
    functionResponse: FunctionResponse;
    fileData?: never;
    executableCode?: never;
    codeExecutionResult?: never;
}
/**
 * Content part interface if the part represents FileData.
 * @public
 */
export interface FileDataPart {
    text?: never;
    inlineData?: never;
    functionCall?: never;
    functionResponse?: never;
    fileData: FileData;
    executableCode?: never;
    codeExecutionResult?: never;
}
/**
 * Content part containing executable code generated by the model.
 * @public
 */
export interface ExecutableCodePart {
    text?: never;
    inlineData?: never;
    functionCall?: never;
    functionResponse?: never;
    fileData?: never;
    executableCode: ExecutableCode;
    codeExecutionResult?: never;
}
/**
 * Content part containing the result of executed code.
 * @public
 */
export interface CodeExecutionResultPart {
    text?: never;
    inlineData?: never;
    functionCall?: never;
    functionResponse?: never;
    fileData?: never;
    executableCode?: never;
    codeExecutionResult: CodeExecutionResult;
}
/**
 * A predicted [FunctionCall] returned from the model
 * that contains a string representing the [FunctionDeclaration.name]
 * and a structured JSON object containing the parameters and their values.
 * @public
 */
export interface FunctionCall {
    name: string;
    args: object;
}
/**
 * The result output from a [FunctionCall] that contains a string
 * representing the [FunctionDeclaration.name]
 * and a structured JSON object containing any output
 * from the function is used as context to the model.
 * This should contain the result of a [FunctionCall]
 * made based on model prediction.
 * @public
 */
export interface FunctionResponse {
    name: string;
    response: object;
}
/**
 * Interface for sending an image.
 * @public
 */
export interface GenerativeContentBlob {
    mimeType: string;
    /**
     * Image as a base64 string.
     */
    data: string;
}
/**
 * Data pointing to a file uploaded with the Files API.
 * @public
 */
export interface FileData {
    mimeType: string;
    fileUri: string;
}
/**
 * Code generated by the model that is meant to be executed, where the result
 * is returned to the model.
 * Only generated when using the code execution tool, in which the code will
 * be automatically executed, and a corresponding `CodeExecutionResult` will
 * also be generated.
 *
 * @public
 */
export interface ExecutableCode {
    /**
     * Programming language of the `code`.
     */
    language: ExecutableCodeLanguage;
    /**
     * The code to be executed.
     */
    code: string;
}
/**
 * @public
 */
export declare enum ExecutableCodeLanguage {
    LANGUAGE_UNSPECIFIED = "language_unspecified",
    PYTHON = "python"
}
/**
 * Result of executing the `ExecutableCode`.
 * Only generated when using code execution, and always follows a `Part`
 * containing the `ExecutableCode`.
 * @public
 */
export interface CodeExecutionResult {
    /**
     * Outcome of the code execution.
     */
    outcome: Outcome;
    /**
     * Contains stdout when code execution is successful, stderr or other
     * description otherwise.
     */
    output: string;
}
/**
 * Possible outcomes of code execution.
 * @public
 */
export declare enum Outcome {
    /**
     * Unspecified status. This value should not be used.
     */
    OUTCOME_UNSPECIFIED = "outcome_unspecified",
    /**
     * Code execution completed successfully.
     */
    OUTCOME_OK = "outcome_ok",
    /**
     * Code execution finished but with a failure. `stderr` should contain the
     * reason.
     */
    OUTCOME_FAILED = "outcome_failed",
    /**
     * Code execution ran for too long, and was cancelled. There may or may not
     * be a partial output present.
     */
    OUTCOME_DEADLINE_EXCEEDED = "outcome_deadline_exceeded"
}
