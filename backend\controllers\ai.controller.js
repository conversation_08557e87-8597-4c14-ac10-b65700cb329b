import * as ai from '../services/ai.services.js';   

// CHECKPOINT: Fixed GET request issue - changed req.body to req.query
export const getResult = async (req, res) => {
    try {
        const { prompt } = req.query;
        if (!prompt) {
            return res.status(400).json({
                error: 'Prompt parameter is required'
            });
        }

        console.log('Received prompt:', prompt);
        console.log('GOOGLE_API_KEY exists:', !!process.env.GOOGLE_API_KEY);

        const result = await ai.generateResult(prompt);
        res.json({ result });
    }
    catch (error) {
        console.error('AI Controller Error:', error);
        res.status(500).json({
            error: error.message
        })
    }
}