import { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import api from '../config/axios';
import 'remixicon/fonts/remixicon.css';
import { intializeSocket, recieveMessage, sendMessage, getActiveUsers, isConnected } from '../config/socket';
import { useUser } from '../context/user.context';

const Project = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [project, setProject] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [showUserPanel, setShowUserPanel] = useState(false);
  const messagesEndRef = useRef(null);

  const [showUserModal, setShowUserModal] = useState(false);
  const [allUsers, setAllUsers] = useState([]);
  const [activeUsers, setActiveUsers] = useState([]);
  const [connectionStatus, setConnectionStatus] = useState(false);
  const { user, setUser } = useUser();
 
  useEffect(() => {
    // Checkpoint: Initialize socket when project is available
    if (project?._id) {
      intializeSocket(project._id);

      // Checkpoint: Set up message listener
      recieveMessage('project-message', (data) => {
        setMessages(prev => [...prev, { ...data, isOwn: false, id: Date.now() }]);
      });

      // Checkpoint: Set up active users listener
      recieveMessage('active-users', (users) => {
        setActiveUsers(users);
      });

      // Checkpoint: Set up user status listener
      recieveMessage('user-status', () => {
        // Handle user status if needed
      });

      // Checkpoint: Monitor connection status
      const checkConnection = () => {
        setConnectionStatus(isConnected());
      };

      checkConnection();
      const connectionInterval = setInterval(checkConnection, 5000);

      // Checkpoint: Get active users after connection
      setTimeout(() => {
        getActiveUsers();
      }, 1000);

      return () => {
        clearInterval(connectionInterval);
      };
    }
  }, [project]);

  useEffect(() => {
    // Checkpoint: Fetch all users for project management
    const fetchAllUsers = async () => {
      try {
        const response = await api.get('/users/all');
        setAllUsers(response.data.allUsers || []);
      } catch (err) {
        setError('Failed to fetch users: ' + (err.response?.data?.error || err.message));
      }
    };

    fetchAllUsers();
  }, []);

  const [selectedUserIds, setSelectedUserIds] = useState([]);

  useEffect(() => {
    // Checkpoint: Validate authentication
    const token = localStorage.getItem('token');
    const userData = localStorage.getItem('user');

    if (!token) return navigate('/login');
    if (userData) setUser(JSON.parse(userData));

    // Checkpoint: Load project data
    if (id) {
      fetchProject();
      loadMessages();
    } else {
      navigate('/');
    }
  }, [id, navigate]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const fetchProject = async () => {
    // Checkpoint: Fetch project details
    try {
      const response = await api.get(`/project/get-project/${id}`);
      setProject(response.data.project);
    } catch (err) {
      setError('Failed to fetch project: ' + (err.response?.data?.error || err.message));
    } finally {
      setLoading(false);
    }
  };

  const loadMessages = async () => {
    // const mockMessages = [
    //   {
    //     id: 1,
    //     user: { name: 'Alice', email: '<EMAIL>' },
    //     message: 'Hey team! Welcome to the project.',
    //     timestamp: new Date(Date.now() - 3600000),
    //     isOwn: false,
    //   },
    //   {
    //     id: 2,
    //     user: { name: 'You', email: user?.email || '<EMAIL>' },
    //     message: 'Thanks, glad to be here!',
    //     timestamp: new Date(Date.now() - 1800000),
    //     isOwn: true,
    //   },
    // ];
    // setMessages(mockMessages);
  };

  const handleSendMessage = () => {
    if (!newMessage.trim()) return;

    const messageData = {
      message: newMessage,
      sender: user?._id,
      projectId: project?._id,
      timestamp: new Date(),
      user: user
    };

    sendMessage('project-message', messageData);

    // Add to local messages for immediate display
    setMessages(prev => [...prev, {
      ...messageData,
      isOwn: true,
      id: Date.now()
    }]);

    setNewMessage('');
  };

 

  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const toggleUserSelection = (userId) => {
    setSelectedUserIds((prev) =>
      prev.includes(userId) ? prev.filter((id) => id !== userId) : [...prev, userId]
    );
  };

  const handleAddCollaborators = async () => {
    if (selectedUserIds.length === 0) {
      setError('Please select at least one user to add');
      return;
    }

    try {
      const response = await api.put('/project/add-user', {
        projectId: id,
        users: selectedUserIds
      });

      // Update the project with new collaborators
      setProject(response.data.project);
      setSelectedUserIds([]);
      setShowUserModal(false);
      setError('');
    } catch (err) {
      setError(err.response?.data?.error || 'Failed to add collaborators');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="text-xl text-gray-600">Loading...</div>
      </div>
    );
  }

  return (
    <div className="flex h-screen w-screen overflow-hidden relative font-sans">
      {/* 👤 User Panel */}
      <AnimatePresence>
        {showUserPanel && (
          <motion.div
            initial={{ x: '-100%' }}
            animate={{ x: 0 }}
            exit={{ x: '-100%' }}
            transition={{ duration: 0.3 }}
            className="absolute top-0 left-0 h-full w-64 bg-white z-50 shadow-lg border-r border-gray-200 p-6"
          >
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold">User Profile</h2>
              <button onClick={() => setShowUserPanel(false)}>
                <i className="ri-close-line text-xl text-gray-600 hover:text-black"></i>
              </button>
            </div>
            <div className="space-y-4">
              {/* User Information */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-sm font-semibold text-gray-800 mb-2">User Information</h3>
                <p className="text-sm text-gray-700">
                  <span className="font-medium">Email:</span> {user?.email || 'Unknown User'}
                </p>
                <div className="mt-2 flex items-center">
                  <span className="text-sm font-medium text-gray-700">Status:</span>
                  <div className="ml-2 flex items-center">
                    <div className={`w-2 h-2 rounded-full mr-1 ${connectionStatus ? 'bg-green-500' : 'bg-red-500'}`}></div>
                    <span className={`text-xs ${connectionStatus ? 'text-green-600' : 'text-red-600'}`}>
                      {connectionStatus ? 'Connected' : 'Disconnected'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Active Users */}
              <div className="bg-green-50 p-4 rounded-lg">
                <h3 className="text-sm font-semibold text-gray-800 mb-2">Active Users ({activeUsers.length})</h3>
                {activeUsers.length > 0 ? (
                  <div className="space-y-2">
                    {activeUsers.map((activeUser, index) => (
                      <div key={index} className="flex items-center text-sm text-gray-700">
                        <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                        <span>{activeUser.email}</span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-gray-600">No active users</p>
                )}
                <button
                  onClick={() => getActiveUsers()}
                  className="mt-2 text-xs bg-green-100 hover:bg-green-200 px-2 py-1 rounded text-green-700"
                >
                  Refresh
                </button>
              </div>

              {/* Project Information */}
              {project && (
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h3 className="text-sm font-semibold text-gray-800 mb-2">Project Information</h3>
                  <p className="text-sm text-gray-700">
                    <span className="font-medium">Project Name:</span> {project.name}
                  </p>
                  <p className="text-sm text-gray-600 mt-1">
                    <span className="font-medium">Total Collaborators:</span> {project.users?.length || 0}
                  </p>
                  {project.users && project.users.length > 0 && (
                    <div className="mt-3">
                      <span className="text-sm font-medium text-gray-600">Collaborators:</span>
                      <div className="mt-2 space-y-1">
                        {project.users.map((collaborator, index) => (
                          <div key={collaborator._id || index} className="text-sm text-gray-700 bg-white px-3 py-2 rounded-md border">
                            📧 {collaborator.email || `User ${index + 1}`}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Quick Actions */}
              <div className="bg-green-50 p-4 rounded-lg">
                <h3 className="text-sm font-semibold text-gray-800 mb-2">Quick Actions</h3>
                <button
                  onClick={() => {
                    setShowUserPanel(false);
                    setShowUserModal(true);
                  }}
                  className="w-full text-left text-sm text-blue-600 hover:text-blue-800 py-1"
                >
                  + Add Collaborators
                </button>
                <button
                  onClick={() => {
                    setShowUserPanel(false);
                    navigate('/');
                  }}
                  className="w-full text-left text-sm text-gray-600 hover:text-gray-800 py-1 mt-1"
                >
                  ← Back to Home
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 💬 Chat Section */}
      <div className="w-full md:w-1/3 bg-[#f7f2f2] flex flex-col justify-between border-r border-gray-300">
        {/* Top Bar */}
        <div className="bg-[#9e7676] p-4 flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <button onClick={() => setShowUserModal(true)}>
              <i className="ri-user-add-line text-black text-2xl cursor-pointer hover:text-white">
                <span className="ml-2 text-lg">Add Collaborator</span>
              </i>
            </button>

            {/* Collaborator Count Badge */}
            {project && (
              <div className="bg-white bg-opacity-20 px-3 py-1 rounded-full">
                <span className="text-white text-sm font-medium">
                  {project.users?.length || 0} collaborator{project.users?.length !== 1 ? 's' : ''}
                </span>
              </div>
            )}
          </div>

          <button onClick={() => setShowUserPanel(true)}>
            <i className="ri-user-3-line text-black text-2xl cursor-pointer hover:text-white"></i>
          </button>
        </div>

        {/* Messages */}
        <div className="flex-1 p-4 space-y-4 overflow-y-auto">
          {messages.map((msg) => (
            <div
              key={msg.id}
              className={`flex flex-col ${msg.isOwn ? 'items-end text-right' : 'items-start'}`}
            >
              <span className="text-xs text-gray-600">{msg.user.email}</span>
              <div
                className={`px-3 py-2 rounded-lg max-w-[80%] text-sm shadow ${
                  msg.isOwn ? 'bg-[#d1c4e9]' : 'bg-[#c8e6c9]'
                }`}
              >
                {msg.message}
                <div className="text-[10px] text-gray-500 mt-1">{formatTime(msg.timestamp)}</div>
              </div>
            </div>
          ))}
          <div ref={messagesEndRef}></div>
        </div>

        {/* Input */}
        <div className="bg-[#d1d1d1] p-4 flex items-center border-t">
          <input
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') handleSendMessage();
            }}
            type="text"
            placeholder="Type your message..."
            className="flex-1 p-2 rounded-md border border-gray-400 bg-white focus:outline-none"
          />
          <button
            onClick={handleSendMessage}
            className="ml-2 px-4 py-2 bg-[#9e7676] text-white rounded-md hover:bg-[#825f5f]"
          >
            Send
          </button>
        </div>
      </div>

      {/* Placeholder Panel */}
      <div className="hidden md:flex flex-1 bg-[#e4e5e7]"></div>

      {/* 👥 Multi-User Selection Modal */}
      <AnimatePresence>
        {showUserModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40 p-4"
          >
            <div className="bg-white w-full max-w-md rounded-lg shadow-lg overflow-hidden">
              {/* Modal Header */}
              <div className="flex justify-between items-center p-4 border-b">
                <h2 className="text-lg font-semibold">Select Collaborators</h2>
                <button onClick={() => setShowUserModal(false)}>
                  <i className="ri-close-line text-xl text-gray-600 hover:text-black"></i>
                </button>
              </div>

              {/* Error Display */}
              {error && (
                <div className="p-4 bg-red-50 border-b border-red-200">
                  <div className="text-red-700 text-sm">{error}</div>
                </div>
              )}

              {/* User List */}
              <div className="max-h-72 overflow-y-auto divide-y">
                {allUsers.length === 0 ? (
                  <div className="p-4 text-center text-gray-500">
                    No users available to add
                  </div>
                ) : (
                  allUsers.map((u) => {
                    const isSelected = selectedUserIds.includes(u._id);
                    return (
                      <div
                        key={u._id}
                        onClick={() => toggleUserSelection(u._id)}
                        className={`flex items-center justify-between p-4 cursor-pointer hover:bg-gray-100 ${
                          isSelected ? 'bg-blue-100' : ''
                        }`}
                      >
                        <div>
                          <p className="text-sm font-medium">{u.email}</p>
                          <p className="text-xs text-gray-500">Available to add</p>
                        </div>
                        <input
                          type="checkbox"
                          readOnly
                          checked={isSelected}
                          className="form-checkbox h-4 w-4 text-blue-500"
                        />
                      </div>
                    );
                  })
                )}
              </div>

              {/* Confirm Button */}
              <div className="p-4 border-t flex justify-between">
                <div className="text-sm text-gray-600">
                  {selectedUserIds.length} user{selectedUserIds.length !== 1 ? 's' : ''} selected
                </div>
                <div className="space-x-2">
                  <button
                    onClick={() => setShowUserModal(false)}
                    className="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleAddCollaborators}
                    disabled={selectedUserIds.length === 0}
                    className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
                  >
                    Add Collaborators
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Debug Info */}
      {selectedUserIds.length > 0 && (
        <div className="absolute bottom-4 left-4 text-sm bg-white shadow px-3 py-1 rounded">
          Selected User IDs: {selectedUserIds.join(', ')}
        </div>
      )}
    </div>
  );
};

export default Project;
