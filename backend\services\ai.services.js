import { GoogleGenerativeAI } from '@google/generative-ai';

// CHECKPOINT: Added error handling and validation
const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);

const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });

export const generateResult = async (prompt) => {
    try {
        if (!process.env.GOOGLE_API_KEY) {
            throw new Error('GOOGLE_API_KEY is not configured');
        }

        if (!prompt || prompt.trim() === '') {
            throw new Error('Prompt cannot be empty');
        }

        console.log('Calling Gemini API with prompt:', prompt.substring(0, 50) + '...');

        const result = await model.generateContent(prompt);
        const response = result.response.text();

        console.log('Gemini API response received successfully');
        return response;
    } catch (error) {
        console.error('Gemini API Error:', error);
        throw error;
    }
}
